#!/usr/bin/env python3
"""
脚本用于处理 DF-Eval 目录下的 CSV 文件，将其按照指定比例划分为训练、验证和测试集。
训练集转换为 JSONL 格式，测试集保存为 CSV 格式。
"""

import os
import json
import pandas as pd
import numpy as np
from pathlib import Path
import argparse

def process_csv_file(csv_path, output_dir, seq_len=96):
    """
    处理单个 CSV 文件，划分数据集并保存
    
    Args:
        csv_path: CSV 文件路径
        output_dir: 输出目录
        seq_len: 序列长度，用于计算边界
    """
    print(f"Processing {csv_path}...")
    
    # 读取 CSV 文件
    df_raw = pd.read_csv(csv_path)
    
    # 计算数据集划分的边界
    num_train = int(len(df_raw) * 0.7)
    num_test = int(len(df_raw) * 0.2)
    num_vali = len(df_raw) - num_train - num_test
    
    # 为训练集、验证集、测试集定义边界
    border1s = [0, num_train - seq_len, len(df_raw) - num_test - seq_len]
    border2s = [num_train, num_train + num_vali, len(df_raw)]
    
    # 获取文件名（不含扩展名）
    file_stem = Path(csv_path).stem
    
    # 处理训练集 (set_type=0)
    border1_train = border1s[0]
    border2_train = border2s[0]
    df_train = df_raw.iloc[border1_train:border2_train]
    
    # 将训练集转换为 JSONL 格式
    train_jsonl_path = output_dir / f"{file_stem}_train.jsonl"
    with open(train_jsonl_path, 'w', encoding='utf-8') as f:
        for idx, row in df_train.iterrows():
            # 提取数值列（跳过日期列）
            sequence_data = []
            for col in df_train.columns:
                if col != 'date' and pd.notna(row[col]):
                    try:
                        val = float(row[col])
                        sequence_data.append(val)
                    except (ValueError, TypeError):
                        continue
            
            # 创建 JSON 对象
            json_obj = {
                "sequence": sequence_data,
                "timestamp": row.get('date', ''),
                "source_file": file_stem
            }
            f.write(json.dumps(json_obj, ensure_ascii=False) + '\n')
    
    # 处理测试集 (set_type=2)
    border1_test = border1s[2]
    border2_test = border2s[2]
    df_test = df_raw.iloc[border1_test:border2_test]
    
    # 保存测试集为 CSV 格式
    test_csv_path = output_dir / f"{file_stem}_test.csv"
    df_test.to_csv(test_csv_path, index=False)
    
    print(f"  - Training set: {len(df_train)} rows -> {train_jsonl_path}")
    print(f"  - Test set: {len(df_test)} rows -> {test_csv_path}")
    
    return len(df_train), len(df_test)

def main():
    parser = argparse.ArgumentParser(description='Process DF-Eval CSV files')
    parser.add_argument('--input_dir', type=str, default='/home/<USER>/Time-MoE/DF-Eval',
                       help='Input directory containing CSV files')
    parser.add_argument('--output_dir', type=str, default='/home/<USER>/Time-MoE/processed_data',
                       help='Output directory for processed files')
    parser.add_argument('--seq_len', type=int, default=96,
                       help='Sequence length for boundary calculation')
    
    args = parser.parse_args()
    
    input_dir = Path(args.input_dir)
    output_dir = Path(args.output_dir)
    
    # 创建输出目录
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # 查找所有 CSV 文件
    csv_files = list(input_dir.rglob('*.csv'))
    
    if not csv_files:
        print(f"No CSV files found in {input_dir}")
        return
    
    print(f"Found {len(csv_files)} CSV files to process")
    
    total_train_rows = 0
    total_test_rows = 0
    processed_files = 0
    
    for csv_file in csv_files:
        try:
            # 为每个子目录创建相应的输出目录
            relative_path = csv_file.relative_to(input_dir)
            sub_output_dir = output_dir / relative_path.parent
            sub_output_dir.mkdir(parents=True, exist_ok=True)
            
            train_rows, test_rows = process_csv_file(csv_file, sub_output_dir, args.seq_len)
            total_train_rows += train_rows
            total_test_rows += test_rows
            processed_files += 1
            
        except Exception as e:
            print(f"Error processing {csv_file}: {e}")
            continue
    
    print(f"\nProcessing completed!")
    print(f"Processed files: {processed_files}/{len(csv_files)}")
    print(f"Total training rows: {total_train_rows}")
    print(f"Total test rows: {total_test_rows}")
    print(f"Output directory: {output_dir}")

if __name__ == "__main__":
    main()
